<?php

declare(strict_types=1);

namespace Comave\EmailConfig\Plugin;

use Comave\EmailConfig\Service\EmailUrlRewriter;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\UrlInterface;

/**
 * Plugin to intercept URL building in email context and rewrite to frontend URLs
 */
class EmailUrlBuilderPlugin
{
    /**
     * @param EmailUrlRewriter $emailUrlRewriter
     * @param State $appState
     */
    public function __construct(
        private readonly EmailUrlRewriter $emailUrlRewriter,
        private readonly State $appState
    ) {
    }

    /**
     * After plugin for getUrl method to rewrite URLs in email context
     *
     * @param UrlInterface $subject
     * @param string $result
     * @param string|null $routePath
     * @param array|null $routeParams
     * @return string
     */
    public function afterGetUrl(
        UrlInterface $subject,
        string $result,
        ?string $routePath = null,
        ?array $routeParams = null
    ): string {
        // Only process in email context (when we're in frontend or admin area sending emails)
        try {
            $areaCode = $this->appState->getAreaCode();
            if ($areaCode !== Area::AREA_FRONTEND && $areaCode !== Area::AREA_ADMINHTML) {
                return $result;
            }
        } catch (\Exception $e) {
            // If we can't determine area, proceed with URL rewriting
        }

        // Check if this is an email-related URL generation by examining the route params
        if (is_array($routeParams) && isset($routeParams['_nosid']) && $routeParams['_nosid'] === true) {
            // This is likely an email URL, rewrite it
            return $this->emailUrlRewriter->rewriteEmailUrls($result);
        }

        // Also check for common email routes
        if ($routePath && $this->isEmailRoute($routePath)) {
            return $this->emailUrlRewriter->rewriteEmailUrls($result);
        }

        return $result;
    }

    /**
     * Check if the route path is commonly used in emails
     *
     * @param string $routePath
     * @return bool
     */
    private function isEmailRoute(string $routePath): bool
    {
        $emailRoutes = [
            'sales/order/view',
            'sales/order/history',
            'sales/order/creditmemo',
            'sales/order/shipment',
            'customer/account/confirm',
            'customer/account/resetpassword',
            'customer/account/index',
            'customer/account',
            'downloadable/download/link',
            'contact',
            'terms-condition-comave',
            'terms-and-conditions',
            'contact-us'
        ];

        foreach ($emailRoutes as $emailRoute) {
            if (strpos($routePath, $emailRoute) !== false) {
                return true;
            }
        }

        return false;
    }
}
