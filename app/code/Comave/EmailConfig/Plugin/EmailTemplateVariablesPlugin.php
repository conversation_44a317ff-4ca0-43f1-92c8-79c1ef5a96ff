<?php

declare(strict_types=1);

namespace Comave\EmailConfig\Plugin;

use Comave\EmailConfig\Service\EmailUrlRewriter;
use Magento\Email\Model\Template;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;

/**
 * Plugin to intercept email template variable processing and add frontend URL variables
 */
class EmailTemplateVariablesPlugin
{
    /**
     * @param EmailUrlRewriter $emailUrlRewriter
     * @param State $appState
     */
    public function __construct(
        private readonly EmailUrlRewriter $emailUrlRewriter,
        private readonly State $appState
    ) {
    }

    /**
     * Before plugin for setVars method to add frontend URL variables
     *
     * @param Template $subject
     * @param array $vars
     * @return array
     */
    public function beforeSetVars(Template $subject, array $vars): array
    {
        // Only process emails in frontend area or when sending emails
        try {
            $areaCode = $this->appState->getAreaCode();
            if ($areaCode !== Area::AREA_FRONTEND && $areaCode !== Area::AREA_ADMINHTML) {
                return [$vars];
            }
        } catch (\Exception $e) {
            // If we can't determine area, proceed with variable processing
        }

        // Add frontend base URL variables for template use
        $vars['frontend_base_url'] = $this->emailUrlRewriter->getFrontendBaseUrl();
        $vars['frontend_secure_url'] = $this->emailUrlRewriter->getFrontendSecureUrl();

        return [$vars];
    }

    /**
     * After plugin for processTemplate method to rewrite URLs in final content
     *
     * @param Template $subject
     * @param string $result
     * @return string
     */
    public function afterProcessTemplate(Template $subject, string $result): string
    {
        // Only process emails in frontend area or when sending emails
        try {
            $areaCode = $this->appState->getAreaCode();
            if ($areaCode !== Area::AREA_FRONTEND && $areaCode !== Area::AREA_ADMINHTML) {
                return $result;
            }
        } catch (\Exception $e) {
            // If we can't determine area, proceed with URL rewriting
        }

        return $this->emailUrlRewriter->rewriteEmailUrls($result);
    }
}
