<?php

declare(strict_types=1);

namespace Comave\EmailConfig\Plugin;

use Comave\EmailConfig\Service\EmailUrlRewriter;
use Magento\Email\Model\Template\Filter;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;

/**
 * Plugin to intercept email template filter processing and rewrite URLs
 */
class EmailTemplateUrlFilter
{
    /**
     * @param EmailUrlRewriter $emailUrlRewriter
     * @param State $appState
     */
    public function __construct(
        private readonly EmailUrlRewriter $emailUrlRewriter,
        private readonly State $appState
    ) {
    }

    /**
     * After plugin for filter method to rewrite URLs in email content
     *
     * @param Filter $subject
     * @param string $result
     * @return string
     */
    public function afterFilter(Filter $subject, string $result): string
    {
        // Only process emails in frontend area or when sending emails
        try {
            $areaCode = $this->appState->getAreaCode();
            if ($areaCode !== Area::AREA_FRONTEND && $areaCode !== Area::AREA_ADMINHTML) {
                return $result;
            }
        } catch (\Exception $e) {
            // If we can't determine area, proceed with URL rewriting
        }

        return $this->emailUrlRewriter->rewriteEmailUrls($result);
    }

    /**
     * Before plugin for urlDirective method to intercept URL generation
     *
     * @param Filter $subject
     * @param array $construction
     * @return array|null
     */
    public function beforeUrlDirective(Filter $subject, array $construction): ?array
    {
        // Only process emails in frontend area or when sending emails
        try {
            $areaCode = $this->appState->getAreaCode();
            if ($areaCode !== Area::AREA_FRONTEND && $areaCode !== Area::AREA_ADMINHTML) {
                return null;
            }
        } catch (\Exception $e) {
            // If we can't determine area, proceed with URL processing
        }

        return null; // Let the original method handle URL generation, we'll rewrite in afterFilter
    }
}
