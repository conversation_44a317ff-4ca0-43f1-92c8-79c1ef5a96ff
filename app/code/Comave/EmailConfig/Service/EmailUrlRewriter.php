<?php

declare(strict_types=1);

namespace Comave\EmailConfig\Service;

use Comave\EmailConfig\Model\Config\Config;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Service to rewrite email URLs from backend domain to frontend domain
 */
class EmailUrlRewriter
{
    private const BACKEND_DOMAINS = [
        'mcstaging.comave.com',
        'mcproduction.comave.com',
        'mcdevelopment.comave.com',
        'mc.comave.com',
        'comave-magento.ddev.site'
    ];

    private const FRONTEND_DOMAIN_MAPPING = [
        'mcstaging.comave.com' => 'staging.comave.com',
        'mcproduction.comave.com' => 'comave.com',
        'mcdevelopment.comave.com' => 'development.comave.com',
        'mc.comave.com' => 'comave.com',
        'comave-magento.ddev.site' => 'dev.comave.com'
    ];

    /**
     * @param Config $config
     * @param ScopeConfigInterface $scopeConfig
     * @param StoreManagerInterface $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly Config $config,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly StoreManagerInterface $storeManager,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Rewrite URLs in email content from backend domain to frontend domain
     *
     * @param string $content
     * @return string
     */
    public function rewriteEmailUrls(string $content): string
    {
        try {
            $frontendBaseUrl = $this->getFrontendBaseUrl();

            if (empty($frontendBaseUrl)) {
                return $content;
            }

            // Remove trailing slash for consistent replacement
            $frontendBaseUrl = rtrim($frontendBaseUrl, '/');

            // Replace all backend domain URLs with frontend domain URLs
            foreach (self::BACKEND_DOMAINS as $backendDomain) {
                // Replace both HTTP and HTTPS versions with various patterns
                $patterns = [
                    'https://' . $backendDomain,
                    'http://' . $backendDomain,
                    '//' . $backendDomain,
                    // Handle URLs in href attributes
                    'href="https://' . $backendDomain,
                    'href="http://' . $backendDomain,
                    "href='https://" . $backendDomain,
                    "href='http://" . $backendDomain,
                    // Handle URLs in action attributes
                    'action="https://' . $backendDomain,
                    'action="http://' . $backendDomain,
                    "action='https://" . $backendDomain,
                    "action='http://" . $backendDomain,
                ];

                $replacements = [
                    $frontendBaseUrl,
                    $frontendBaseUrl,
                    $frontendBaseUrl,
                    // Handle URLs in href attributes
                    'href="' . $frontendBaseUrl,
                    'href="' . $frontendBaseUrl,
                    "href='" . $frontendBaseUrl,
                    "href='" . $frontendBaseUrl,
                    // Handle URLs in action attributes
                    'action="' . $frontendBaseUrl,
                    'action="' . $frontendBaseUrl,
                    "action='" . $frontendBaseUrl,
                    "action='" . $frontendBaseUrl,
                ];

                $content = str_replace($patterns, $replacements, $content);
            }

            return $content;
        } catch (\Exception $e) {
            $this->logger->error('Error rewriting email URLs: ' . $e->getMessage());
            return $content;
        }
    }

    /**
     * Get frontend base URL for current store
     *
     * @return string
     */
    public function getFrontendBaseUrl(): string
    {
        try {
            $store = $this->storeManager->getStore();
            
            // First try to get from custom frontend configuration
            $frontendUrl = $this->config->getFrontendSecureUrl(ScopeInterface::SCOPE_STORE, $store->getCode());
            
            if (!empty($frontendUrl) && $frontendUrl !== '{{secure_base_url}}') {
                return $frontendUrl;
            }

            // Fallback to mapping based on current backend domain
            $currentBaseUrl = $store->getBaseUrl(UrlInterface::URL_TYPE_LINK);
            $currentDomain = parse_url($currentBaseUrl, PHP_URL_HOST);
            
            if ($currentDomain && isset(self::FRONTEND_DOMAIN_MAPPING[$currentDomain])) {
                $frontendDomain = self::FRONTEND_DOMAIN_MAPPING[$currentDomain];
                $scheme = parse_url($currentBaseUrl, PHP_URL_SCHEME) ?: 'https';
                return $scheme . '://' . $frontendDomain;
            }

            // Final fallback to store base URL
            return $store->getBaseUrl(UrlInterface::URL_TYPE_LINK);
        } catch (\Exception $e) {
            $this->logger->error('Error getting frontend base URL: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * Get frontend secure URL for current store
     *
     * @return string
     */
    public function getFrontendSecureUrl(): string
    {
        try {
            $store = $this->storeManager->getStore();
            
            // First try to get from custom frontend configuration
            $frontendUrl = $this->config->getFrontendSecureUrl(ScopeInterface::SCOPE_STORE, $store->getCode());
            
            if (!empty($frontendUrl) && $frontendUrl !== '{{secure_base_url}}') {
                return $frontendUrl;
            }

            // Fallback to mapping based on current backend domain
            $currentBaseUrl = $store->getBaseUrl(UrlInterface::URL_TYPE_WEB);
            $currentDomain = parse_url($currentBaseUrl, PHP_URL_HOST);
            
            if ($currentDomain && isset(self::FRONTEND_DOMAIN_MAPPING[$currentDomain])) {
                $frontendDomain = self::FRONTEND_DOMAIN_MAPPING[$currentDomain];
                return 'https://' . $frontendDomain;
            }

            // Final fallback to store secure base URL
            return $store->getBaseUrl(UrlInterface::URL_TYPE_WEB);
        } catch (\Exception $e) {
            $this->logger->error('Error getting frontend secure URL: ' . $e->getMessage());
            return '';
        }
    }
}
