<?php

declare(strict_types=1);

namespace Comave\EmailConfig\Console\Command;

use Comave\EmailConfig\Service\EmailUrlRewriter;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Console command to test email URL rewriting functionality
 */
class TestEmailUrlRewrite extends Command
{
    /**
     * @param EmailUrlRewriter $emailUrlRewriter
     */
    public function __construct(
        private readonly EmailUrlRewriter $emailUrlRewriter
    ) {
        parent::__construct();
    }

    /**
     * Configure the command
     */
    protected function configure(): void
    {
        $this->setName('comave:email:test-url-rewrite')
            ->setDescription('Test email URL rewriting functionality');
    }

    /**
     * Execute the command
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Testing Email URL Rewriting...</info>');

        // Test sample email content with backend URLs
        $testContent = '
            <p>Thank you for your order!</p>
            <a href="https://mcstaging.comave.com/sales/order/view/order_id/123">View Order</a>
            <a href="https://mcstaging.comave.com/sales/order/history/">Order History</a>
            <a href="https://mcstaging.comave.com/customer/account/">My Account</a>
            <a href="https://comave-magento.ddev.site/sales/order/view/order_id/456">View Order (DDEV)</a>
            <form action="https://mcstaging.comave.com/customer/account/resetpassword">
                <input type="submit" value="Reset Password">
            </form>
        ';

        $output->writeln('<comment>Original content:</comment>');
        $output->writeln($testContent);

        $rewrittenContent = $this->emailUrlRewriter->rewriteEmailUrls($testContent);

        $output->writeln('<comment>Rewritten content:</comment>');
        $output->writeln($rewrittenContent);

        $frontendBaseUrl = $this->emailUrlRewriter->getFrontendBaseUrl();
        $output->writeln('<info>Frontend Base URL: ' . $frontendBaseUrl . '</info>');

        $frontendSecureUrl = $this->emailUrlRewriter->getFrontendSecureUrl();
        $output->writeln('<info>Frontend Secure URL: ' . $frontendSecureUrl . '</info>');

        $output->writeln('<info>Email URL rewriting test completed!</info>');

        return Command::SUCCESS;
    }
}
