<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Plugin to intercept email template processing and replace URLs -->
    <type name="Magento\Email\Model\Template\Filter">
        <plugin name="comave_email_config_url_filter" type="Comave\EmailConfig\Plugin\EmailTemplateUrlFilter" sortOrder="100" />
    </type>

    <!-- Plugin to intercept email template variable processing -->
    <type name="Magento\Email\Model\Template">
        <plugin name="comave_email_config_template_variables" type="Comave\EmailConfig\Plugin\EmailTemplateVariablesPlugin" sortOrder="100" />
    </type>

    <!-- Register console commands -->
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="comave_email_test_url_rewrite" xsi:type="object">Comave\EmailConfig\Console\Command\TestEmailUrlRewrite</item>
            </argument>
        </arguments>
    </type>
</config>
