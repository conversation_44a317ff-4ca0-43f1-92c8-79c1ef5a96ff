<?php

declare(strict_types=1);

namespace Comave\Marketplace\Console\Command;

use Magento\Framework\Setup\ModuleDataSetupInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Console command to test email URL rewriting functionality
 */
class TestEmailUrlRewrite extends Command
{
    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup
    ) {
        parent::__construct();
    }

    /**
     * Configure the command
     */
    protected function configure(): void
    {
        $this->setName('comave:email:test-url-rewrite')
            ->setDescription('Test email URL rewriting functionality by checking email templates');
    }

    /**
     * Execute the command
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Testing Email URL Rewriting in Database...</info>');

        $connection = $this->moduleDataSetup->getConnection();
        $tableName = $this->moduleDataSetup->getTable('email_template');
        
        // Get all email templates
        $select = $connection->select()->from($tableName, ['template_id', 'template_code', 'template_text']);
        $templates = $connection->fetchAll($select);
        
        $output->writeln('<comment>Found ' . count($templates) . ' email templates</comment>');
        
        $backendDomains = [
            'mcstaging.comave.com',
            'mcproduction.comave.com',
            'mcdevelopment.comave.com',
            'mc.comave.com',
            'comave-magento.ddev.site'
        ];
        
        $foundIssues = 0;
        $fixedTemplates = 0;
        
        foreach ($templates as $template) {
            $hasBackendUrls = false;
            
            foreach ($backendDomains as $domain) {
                if (strpos($template['template_text'], $domain) !== false) {
                    $hasBackendUrls = true;
                    break;
                }
            }
            
            if ($hasBackendUrls) {
                $foundIssues++;
                $output->writeln('<error>Template ID ' . $template['template_id'] . ' (' . $template['template_code'] . ') contains backend URLs</error>');

                // Show which backend URLs are found
                foreach ($backendDomains as $domain) {
                    if (strpos($template['template_text'], $domain) !== false) {
                        $output->writeln('<comment>  - Contains: ' . $domain . '</comment>');
                    }
                }
            } else {
                $fixedTemplates++;
            }
        }
        
        $output->writeln('<info>Summary:</info>');
        $output->writeln('<info>- Total templates: ' . count($templates) . '</info>');
        $output->writeln('<info>- Templates with backend URLs: ' . $foundIssues . '</info>');
        $output->writeln('<info>- Templates with correct URLs: ' . $fixedTemplates . '</info>');
        
        if ($foundIssues === 0) {
            $output->writeln('<info>✅ All email templates have correct frontend URLs!</info>');
        } else {
            $output->writeln('<comment>⚠️  Some templates still contain backend URLs. Run setup:upgrade to fix them.</comment>');
        }

        return Command::SUCCESS;
    }
}
