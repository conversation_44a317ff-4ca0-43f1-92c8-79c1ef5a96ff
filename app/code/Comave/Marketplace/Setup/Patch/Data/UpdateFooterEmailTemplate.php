<?php

declare(strict_types=1);

namespace Comave\Marketplace\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Module\Dir;
use Magento\Framework\Module\Dir\Reader as DirReader;
use Magento\Framework\Filesystem\Io\File as IoFile;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Psr\Log\LoggerInterface;

/**
 * Patch class to update all email templates with correct frontend URLs
 */
class UpdateFooterEmailTemplate implements DataPatchInterface
{
    /**
     * The file name of the email footer template to load and inject.
     */
    private const EMAIL_TEMPLATE_FILE = 'email_footer_template.html';

    /**
     * The original template code used to identify the correct email template to update.
     */
    private const ORIG_TEMPLATE_CODE = 'design_email_footer_template';

    /**
     * Directory inside the module where the source HTML template is located.
     */
    private const DEFAULT_SOURCE_DIR = 'install-data';

    /**
     * Backend to frontend domain mapping
     */
    private const DOMAIN_MAPPING = [
        'mcstaging.comave.com' => 'staging.comave.com',
        'mcproduction.comave.com' => 'comave.com',
        'mcdevelopment.comave.com' => 'development.comave.com',
        'mc.comave.com' => 'comave.com',
        'comave-magento.ddev.site' => 'dev.comave.com'
    ];

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param DirReader $dirReader
     * @param IoFile $ioFile
     * @param ScopeConfigInterface $scopeConfig
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly ModuleDataSetupInterface $moduleDataSetup,
        private readonly DirReader $dirReader,
        private readonly IoFile $ioFile,
        private readonly ScopeConfigInterface $scopeConfig,
        private readonly LoggerInterface $logger
    ) {
    }

    /**
     * Apply the patch: update footer template and fix all email template URLs
     *
     * @return void
     */
    public function apply(): void
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        // Update footer template
        $this->updateFooterTemplate();

        // Fix all email template URLs (run multiple times to ensure all are fixed)
        for ($i = 0; $i < 3; $i++) {
            $this->fixAllEmailTemplateUrls();
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * Update the footer email template
     *
     * @return void
     */
    private function updateFooterTemplate(): void
    {
        $htmlContent = $this->getFileContent(self::EMAIL_TEMPLATE_FILE);
        if (!$htmlContent) {
            return;
        }

        $frontendBaseUrl = $this->getFrontendBaseUrl();
        $htmlContent = str_replace('{{frontend_base_url}}', rtrim($frontendBaseUrl, '/'), $htmlContent);

        try {
            $this->moduleDataSetup->getConnection()->update(
                $this->moduleDataSetup->getTable('email_template'),
                ['template_text' => $htmlContent],
                ['orig_template_code = ?' => self::ORIG_TEMPLATE_CODE]
            );
        } catch (CouldNotSaveException $e) {
            $this->logger->error('Failed to update the email footer template: ' . $e->getMessage());
        }
    }

    /**
     * Fix URLs in all email templates
     *
     * @return void
     */
    private function fixAllEmailTemplateUrls(): void
    {
        try {
            $connection = $this->moduleDataSetup->getConnection();
            $tableName = $this->moduleDataSetup->getTable('email_template');
            
            // Get all email templates
            $select = $connection->select()->from($tableName, ['template_id', 'template_text']);
            $templates = $connection->fetchAll($select);
            
            $frontendBaseUrl = rtrim($this->getFrontendBaseUrl(), '/');
            
            foreach ($templates as $template) {
                $originalContent = $template['template_text'];
                $updatedContent = $this->rewriteEmailUrls($originalContent, $frontendBaseUrl);
                
                if ($originalContent !== $updatedContent) {
                    $connection->update(
                        $tableName,
                        ['template_text' => $updatedContent],
                        ['template_id = ?' => $template['template_id']]
                    );
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('Failed to fix email template URLs: ' . $e->getMessage());
        }
    }

    /**
     * Rewrite URLs in email content
     *
     * @param string $content
     * @param string $frontendBaseUrl
     * @return string
     */
    private function rewriteEmailUrls(string $content, string $frontendBaseUrl): string
    {
        foreach (self::DOMAIN_MAPPING as $backendDomain => $frontendDomain) {
            $patterns = [
                'https://' . $backendDomain,
                'http://' . $backendDomain,
                'href="https://' . $backendDomain,
                'href="http://' . $backendDomain,
                "href='https://" . $backendDomain,
                "href='http://" . $backendDomain,
                'action="https://' . $backendDomain,
                'action="http://' . $backendDomain,
            ];

            $replacements = [
                $frontendBaseUrl,
                $frontendBaseUrl,
                'href="' . $frontendBaseUrl,
                'href="' . $frontendBaseUrl,
                "href='" . $frontendBaseUrl,
                "href='" . $frontendBaseUrl,
                'action="' . $frontendBaseUrl,
                'action="' . $frontendBaseUrl,
            ];

            $content = str_replace($patterns, $replacements, $content);
        }

        return $content;
    }

    /**
     * Get frontend base URL
     *
     * @return string
     */
    private function getFrontendBaseUrl(): string
    {
        // Try to get from configuration first
        $frontendUrl = $this->scopeConfig->getValue(
            'web/secure/frontend_base_url',
            ScopeInterface::SCOPE_DEFAULT
        );

        if (!empty($frontendUrl) && $frontendUrl !== '{{secure_base_url}}') {
            return $frontendUrl;
        }

        // Fallback to current base URL and map it
        $currentBaseUrl = $this->scopeConfig->getValue(
            'web/secure/base_url',
            ScopeInterface::SCOPE_DEFAULT
        );

        $currentDomain = parse_url($currentBaseUrl, PHP_URL_HOST);
        
        if ($currentDomain && isset(self::DOMAIN_MAPPING[$currentDomain])) {
            $frontendDomain = self::DOMAIN_MAPPING[$currentDomain];
            return 'https://' . $frontendDomain . '/';
        }

        return $currentBaseUrl ?: 'https://staging.comave.com/';
    }

    /**
     * Get the full path to the directory containing email template source files.
     *
     * @return string
     */
    private function getSourceDir(): string
    {
        return $this->dirReader->getModuleDir(Dir::MODULE_ETC_DIR, 'Comave_Marketplace')
            . DIRECTORY_SEPARATOR . self::DEFAULT_SOURCE_DIR . DIRECTORY_SEPARATOR;
    }

    /**
     * Load the contents of the given template file.
     *
     * @param string $file
     * @return string
     */
    private function getFileContent(string $file): string
    {
        try {
            return $this->ioFile->read($this->getSourceDir() . $file);
        } catch (\Exception $e) {
            $this->logger->error(sprintf('Template file "%s" does not exist. Error: %s', $file, $e->getMessage()));
            return '';
        }
    }

    /**
     * @inheritdoc
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @inheritdoc
     */
    public function getAliases(): array
    {
        return [];
    }
}
