<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Mail\Template\TransportBuilder">
        <plugin name="comave_marketplace_forgot_email_transport_builder_plugin" type="Comave\Marketplace\Plugin\TransportBuilderPlugin" sortOrder="10" />
    </type>
    <type name="Magento\Customer\Controller\Account\ResetPasswordPost">
        <plugin name="comave_marketplace_reset_password_post_plugin"
                type="Comave\Marketplace\Plugin\ResetPasswordPostPlugin"
                sortOrder="10" />
    </type>
    <preference for="Comave\Marketplace\Api\Url\FrontendUrlInterface" type="Comave\Marketplace\Model\Url\FrontendUrl" />

    <type name="Magento\CatalogGraphQl\Model\Resolver\Products\DataProvider\Product\CompositeCollectionProcessor">
        <arguments>
            <argument name="collectionProcessors" xsi:type="array">
                <item name="marketplaceApproval" xsi:type="object">Comave\Marketplace\Model\Resolver\CollectionProcessor\ApprovedProducts</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Catalog\Model\Product\Attribute\OptionManagement">
        <plugin name="restrict_add_attribute_option"
                type="Comave\Marketplace\Plugin\RestrictAddAttributeOption"
                sortOrder="1"/>
    </type>
    <type name="Comave\SellerPayouts\Controller\Seller\Verify">
        <plugin name="comave_block_stripe_verify"
                type="Comave\Marketplace\Plugin\BlockSellerStripeActions" />
    </type>

    <!-- Register console commands -->
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="comave_marketplace_test_email_url_rewrite" xsi:type="object">Comave\Marketplace\Console\Command\TestEmailUrlRewrite</item>
            </argument>
        </arguments>
    </type>
</config>
